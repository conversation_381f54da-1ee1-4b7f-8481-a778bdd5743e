import pygame
import sys
import time
import argparse
import random
import csv
from datetime import datetime

class LatencyTest:
    def __init__(self, delay=200, fullscreen=False, test_mode="manual", cycles=10, random_delay=False):
        # Initialize pygame
        pygame.init()

        # Set up the display
        if fullscreen:
            self.screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
            self.width, self.height = self.screen.get_size()
        else:
            self.width, self.height = 800, 600
            self.screen = pygame.display.set_mode((self.width, self.height))

        pygame.display.set_caption(f"Advanced Latency Test - {delay}ms")

        # Colors
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.RED = (255, 0, 0)
        self.GREEN = (0, 255, 0)
        self.BLUE = (0, 0, 255)

        # Test parameters
        self.base_delay = delay  # Base delay in milliseconds
        self.test_mode = test_mode  # "manual", "auto", or "random"
        self.cycles = cycles  # Number of test cycles for auto mode
        self.random_delay = random_delay  # Whether to use random delays

        # State variables
        self.current_color = self.BLACK
        self.target_color = self.WHITE
        self.click_time = 0
        self.change_time = 0
        self.waiting_for_change = False
        self.current_delay = delay
        self.cycle_count = 0
        self.results = []

        # Auto test variables
        self.auto_test_running = False
        self.auto_test_next_click = 0

        # Font for displaying information
        self.font = pygame.font.SysFont('Arial', 24)
        self.small_font = pygame.font.SysFont('Arial', 18)

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return False
                elif event.key == pygame.K_r:
                    # Reset on 'R' key press (still available as a manual option)
                    self.reset_test()
                elif event.key == pygame.K_a and not self.auto_test_running:
                    # Start auto test on 'A' key press
                    self.start_auto_test()
                elif event.key == pygame.K_s:
                    # Save results on 'S' key press
                    self.save_results()
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1 and not self.waiting_for_change and self.current_color == self.BLACK and not self.auto_test_running:
                    # Manual click while screen is black
                    self.handle_click()
            elif event.type == pygame.USEREVENT:
                # Auto-reset timer event
                if self.current_color != self.BLACK:
                    self.reset_test()
        return True

    def handle_click(self):
        self.click_time = time.time()
        self.waiting_for_change = True

        # Set a random delay if enabled
        if self.random_delay:
            self.current_delay = random.randint(max(50, self.base_delay - 100), self.base_delay + 100)
        else:
            self.current_delay = self.base_delay

        # Choose a random target color for variety
        colors = [self.WHITE, self.RED, self.GREEN, self.BLUE]
        self.target_color = random.choice(colors)

        print(f"Click detected at {self.click_time:.3f}, delay set to {self.current_delay}ms")

    def update(self):
        current_time = time.time()

        # Handle auto test mode
        if self.auto_test_running:
            if not self.waiting_for_change and current_time >= self.auto_test_next_click:
                self.handle_click()

            if self.cycle_count >= self.cycles:
                self.auto_test_running = False
                print(f"Auto test completed: {self.cycle_count} cycles")

        # Check if it's time to change color
        if self.waiting_for_change:
            elapsed_ms = (current_time - self.click_time) * 1000

            if elapsed_ms >= self.current_delay:
                self.current_color = self.target_color
                self.change_time = current_time
                actual_delay = (self.change_time - self.click_time) * 1000

                # Record the result
                self.results.append({
                    'cycle': self.cycle_count + 1,
                    'target_delay': self.current_delay,
                    'actual_delay': actual_delay,
                    'difference': actual_delay - self.current_delay
                })

                self.cycle_count += 1
                self.waiting_for_change = False

                print(f"Screen changed at {self.change_time:.3f}")
                print(f"Actual delay: {actual_delay:.2f}ms (target: {self.current_delay}ms)")

                # Auto-reset after a short delay (1.5 seconds)
                pygame.time.set_timer(pygame.USEREVENT, 1500, True)

                # Schedule next click for auto test
                if self.auto_test_running:
                    self.auto_test_next_click = current_time + 2.0  # 2 second pause between tests

    def render(self):
        # Fill the screen with current color
        self.screen.fill(self.current_color)

        # Display information
        if self.current_color != self.BLACK:
            # Show the actual delay on colored background
            text_color = self.BLACK if self.current_color == self.WHITE else self.WHITE

            if len(self.results) > 0:
                last_result = self.results[-1]
                delay_text = self.font.render(f"Delay: {last_result['actual_delay']:.2f}ms (Target: {last_result['target_delay']}ms)", True, text_color)
                diff_text = self.font.render(f"Difference: {last_result['difference']:.2f}ms", True, text_color)
                self.screen.blit(delay_text, (20, 20))
                self.screen.blit(diff_text, (20, 60))

            instruction_text = self.font.render("Auto-reset in 1.5s, 'S' to save results, ESC to quit", True, text_color)
        else:
            # Show instructions on black background
            if self.waiting_for_change:
                status_text = self.font.render(f"Changing in {self.current_delay}ms...", True, self.WHITE)
                self.screen.blit(status_text, (self.width // 2 - status_text.get_width() // 2, self.height // 2))

            delay_text = self.font.render(f"Target delay: {self.base_delay}ms", True, self.WHITE)
            self.screen.blit(delay_text, (20, 20))

            if self.auto_test_running:
                status_text = self.font.render(f"Auto test running: {self.cycle_count}/{self.cycles} cycles", True, self.WHITE)
                self.screen.blit(status_text, (20, 60))
                instruction_text = self.font.render("Press 'ESC' to cancel", True, self.WHITE)
            else:
                mode_text = self.font.render(f"Mode: {'Random delay' if self.random_delay else 'Fixed delay'}", True, self.WHITE)
                self.screen.blit(mode_text, (20, 60))
                instruction_text = self.font.render("Click to test, 'A' for auto test, ESC to quit", True, self.WHITE)

        # Position and display the instruction text
        self.screen.blit(instruction_text, (20, self.height - 50))

        # Display results summary if we have results
        if len(self.results) > 0:
            # Calculate statistics
            delays = [r['actual_delay'] for r in self.results]
            avg_delay = sum(delays) / len(delays)
            min_delay = min(delays)
            max_delay = max(delays)

            # Display statistics
            y_pos = 100
            stats_text = self.small_font.render(f"Tests: {len(self.results)}, Avg: {avg_delay:.2f}ms, Min: {min_delay:.2f}ms, Max: {max_delay:.2f}ms",
                                              True, self.WHITE if self.current_color == self.BLACK else self.BLACK)
            self.screen.blit(stats_text, (20, y_pos))

            # Display last 5 results
            y_pos += 30
            header = self.small_font.render("Recent results:", True, self.WHITE if self.current_color == self.BLACK else self.BLACK)
            self.screen.blit(header, (20, y_pos))

            for i, result in enumerate(self.results[-5:]):
                y_pos += 25
                result_text = self.small_font.render(
                    f"{len(self.results)-len(self.results[-5:])+i+1}. Target: {result['target_delay']}ms, Actual: {result['actual_delay']:.2f}ms, Diff: {result['difference']:.2f}ms",
                    True, self.WHITE if self.current_color == self.BLACK else self.BLACK)
                self.screen.blit(result_text, (20, y_pos))

        # Update the display
        pygame.display.flip()

    def reset_test(self):
        self.current_color = self.BLACK
        self.waiting_for_change = False
        print("Test reset")

    def start_auto_test(self):
        self.auto_test_running = True
        self.cycle_count = 0
        self.auto_test_next_click = time.time() + 1.0  # Start first click after 1 second
        print(f"Starting auto test: {self.cycles} cycles")

    def save_results(self):
        if len(self.results) == 0:
            print("No results to save")
            return

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"latency_results_{timestamp}.csv"

        # Write results to CSV
        with open(filename, 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'target_delay', 'actual_delay', 'difference']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for result in self.results:
                writer.writerow(result)

        print(f"Results saved to {filename}")

    def run(self):
        running = True
        while running:
            running = self.handle_events()
            self.update()
            self.render()
            pygame.time.delay(10)  # Small delay to reduce CPU usage

        pygame.quit()
        sys.exit()

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Advanced Latency Test Tool')
    parser.add_argument('--delay', type=int, default=200,
                        help='Delay in milliseconds between click and screen change (default: 200)')
    parser.add_argument('--fullscreen', action='store_true',
                        help='Run in fullscreen mode')
    parser.add_argument('--mode', choices=['manual', 'auto', 'random'], default='manual',
                        help='Test mode: manual, auto, or random (default: manual)')
    parser.add_argument('--cycles', type=int, default=10,
                        help='Number of test cycles for auto mode (default: 10)')
    parser.add_argument('--random', action='store_true',
                        help='Use random delays around the base delay')
    args = parser.parse_args()

    # Create and run the test
    test = LatencyTest(
        delay=args.delay,
        fullscreen=args.fullscreen,
        test_mode=args.mode,
        cycles=args.cycles,
        random_delay=args.random
    )
    test.run()

if __name__ == "__main__":
    main()
