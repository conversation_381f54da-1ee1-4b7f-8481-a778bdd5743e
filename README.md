# Latency Test Tools

This repository contains Python scripts for testing and verifying the accuracy of latency detection applications.

## Requirements

- Python 3.6 or higher
- Pygame library

## Installation

1. Install Python from [python.org](https://www.python.org/downloads/)
2. Install Pygame:
   ```
   pip install pygame
   ```

## Basic Latency Test Tool

The `latency_test.py` script provides a simple way to test latency detection. It displays a black screen that changes to white after a specified delay when you click.

### Usage

```
python latency_test.py [--delay MILLISECONDS] [--fullscreen]
```

### Options

- `--delay`: Set the delay in milliseconds between click and screen change (default: 200ms)
- `--fullscreen`: Run in fullscreen mode

### Controls

- **Left Mouse Button**: Click to trigger the screen change after the delay
- **ESC key**: Quit the application

The screen automatically resets to black 1.5 seconds after changing to white, so you can immediately click again for another test.

## Advanced Latency Test Tool

The `advanced_latency_test.py` script provides more features for comprehensive latency testing:

- Multiple test modes (manual, auto)
- Random or fixed delays
- Statistics tracking
- Results export to CSV
- Multiple color changes

### Usage

```
python advanced_latency_test.py [--delay MILLISECONDS] [--fullscreen] [--mode MODE] [--cycles COUNT] [--random]
```

### Options

- `--delay`: Set the base delay in milliseconds (default: 200ms)
- `--fullscreen`: Run in fullscreen mode
- `--mode`: Test mode: 'manual', 'auto', or 'random' (default: 'manual')
- `--cycles`: Number of test cycles for auto mode (default: 10)
- `--random`: Use random delays around the base delay

### Controls

- **Left Mouse Button**: Click to trigger the screen change (in manual mode)
- **A key**: Start automatic test sequence
- **S key**: Save test results to CSV file
- **ESC key**: Quit the application

The screen automatically resets to black 1.5 seconds after changing color, so you can immediately click again for another test without needing to manually reset.

## How to Use for Verifying Your Android App

1. Position your Android device with the latency detection app so its camera can see your computer screen
2. Run one of the latency test scripts on your computer
3. Start your Android app's latency detection
4. Click on the black screen in the Python app
5. The screen will change after the specified delay
6. Compare the delay reported by the Python script with what your Android app detected
7. Repeat multiple times to verify consistency

## Tips for Accurate Testing

1. Use fullscreen mode for the most consistent results
2. Position your Android device's camera to clearly see the screen
3. Minimize ambient light changes during testing
4. For the most accurate results, use a high refresh rate monitor
5. Run the auto test mode to collect a statistically significant sample
6. Export and analyze the results to evaluate your app's accuracy
