import pygame
import sys
import time
import argparse

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Latency test tool')
    parser.add_argument('--delay', type=int, default=200,
                        help='Delay in milliseconds between click and screen change (default: 200)')
    parser.add_argument('--fullscreen', action='store_true',
                        help='Run in fullscreen mode')
    args = parser.parse_args()

    # Initialize pygame
    pygame.init()

    # Set up the display
    if args.fullscreen:
        screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
        width, height = screen.get_size()
    else:
        width, height = 800, 600
        screen = pygame.display.set_mode((width, height))

    pygame.display.set_caption(f"Latency Test - {args.delay}ms")

    # Colors
    BLACK = (0, 0, 0)
    WHITE = (255, 255, 255)

    # Initial state
    current_color = BLACK
    click_time = 0
    change_time = 0
    waiting_for_change = False
    delay_ms = args.delay  # Delay in milliseconds

    # Font for displaying information
    font = pygame.font.SysFont('Arial', 24)

    # Main loop
    running = True
    while running:
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_r:
                    # Reset to black on 'R' key press (still available as a manual option)
                    current_color = BLACK
                    waiting_for_change = False
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1 and not waiting_for_change and current_color == BLACK:
                    # Left mouse button clicked while screen is black
                    click_time = time.time()
                    waiting_for_change = True
                    print(f"Click detected at {click_time:.3f}")
            elif event.type == pygame.USEREVENT:
                # Auto-reset timer event
                if current_color == WHITE:
                    current_color = BLACK
                    print("Auto-reset to black")

        # Check if it's time to change color
        if waiting_for_change:
            current_time = time.time()
            elapsed_ms = (current_time - click_time) * 1000

            if elapsed_ms >= delay_ms:
                current_color = WHITE
                change_time = current_time
                actual_delay = (change_time - click_time) * 1000
                print(f"Screen changed at {change_time:.3f}")
                print(f"Actual delay: {actual_delay:.2f}ms (target: {delay_ms}ms)")
                waiting_for_change = False

                # Set a timer to auto-reset after 1.5 seconds
                pygame.time.set_timer(pygame.USEREVENT, 1500, True)

        # Fill the screen with current color
        screen.fill(current_color)

        # Display information
        if current_color == WHITE:
            # Show the actual delay on white background (black text)
            actual_delay = (change_time - click_time) * 1000
            delay_text = font.render(f"Delay: {actual_delay:.2f}ms", True, BLACK)
            instruction_text = font.render("Auto-reset in 1.5s, ESC to quit", True, BLACK)
        else:
            # Show instructions on black background (white text)
            if waiting_for_change:
                status_text = font.render(f"Changing in {delay_ms}ms...", True, WHITE)
                screen.blit(status_text, (width // 2 - status_text.get_width() // 2, height // 2))
            delay_text = font.render(f"Target delay: {delay_ms}ms", True, WHITE)
            instruction_text = font.render("Click anywhere to test, ESC to quit", True, WHITE)

        # Position and display the text
        screen.blit(delay_text, (20, 20))
        screen.blit(instruction_text, (20, height - 50))

        # Update the display
        pygame.display.flip()

    # Clean up
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()
