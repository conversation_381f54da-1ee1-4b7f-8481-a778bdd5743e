package com.example.ldat_camera.latency

import android.content.Context
import android.util.Log
import androidx.camera.core.Camera
import androidx.camera.core.CameraControl
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExposureState
import androidx.camera.core.FocusMeteringAction
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.core.SurfaceOrientedMeteringPointFactory
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.example.ldat_camera.detection.DirectYuvAnalyzer
import com.example.ldat_camera.detection.LatencyDetector
import com.example.ldat_camera.detection.RegionOfInterest
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * Manages camera operations and latency detection
 */
class LatencyManager(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val previewView: PreviewView,
    private val latencyListener: LatencyDetector.LatencyDetectionListener
) {
    private var camera: Camera? = null
    private var cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()
    private var imageAnalyzer: ImageAnalysis? = null
    private var latencyDetector: LatencyDetector? = null
    private var directYuvAnalyzer: DirectYuvAnalyzer? = null
    private var regionOfInterest: RegionOfInterest? = null

    private val TAG = "LatencyManager"

    /**
     * Start the camera with latency detection
     */
    fun startCamera() {
        Log.d(TAG, "Starting camera")

        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

        cameraProviderFuture.addListener({
            try {
                val cameraProvider = cameraProviderFuture.get()

                // Set up the preview use case
                val preview = Preview.Builder()
                    .build()
                    .also {
                        it.setSurfaceProvider(previewView.surfaceProvider)
                    }

                // Set up the image analyzer with optimized settings
                imageAnalyzer = ImageAnalysis.Builder()
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_YUV_420_888) // Optimal format for YUV processing
                    .build()

                // Create the direct YUV analyzer for maximum efficiency
                directYuvAnalyzer = DirectYuvAnalyzer(
                    object : DirectYuvAnalyzer.LatencyDetectionListener {
                        override fun onFrameProcessed(
                            timestamp: Long,
                            luminance: Float,
                            dominantColor: Int,
                            luminanceDiff: Float,
                            colorDiff: Float,
                            isLuminanceChange: Boolean,
                            isColorChange: Boolean,
                            frameRate: Float
                        ) {
                            // Forward the events to the original listener
                            latencyListener.onFrameProcessed(
                                timestamp, luminance, dominantColor,
                                luminanceDiff, colorDiff, isLuminanceChange, isColorChange,
                                frameRate
                            )
                        }
                    },
                    0.015f,  // Luminance threshold (1.5% change)
                    0.02f,   // Color threshold (2% change)
                    1        // Process every frame for maximum responsiveness
                )

                // Use the direct YUV analyzer instead of the bitmap-based one
                imageAnalyzer?.setAnalyzer(cameraExecutor, directYuvAnalyzer!!)

                // Select back camera as a default
                val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

                // Unbind any bound use cases before rebinding
                cameraProvider.unbindAll()

                // Bind use cases to camera
                camera = cameraProvider.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    preview,
                    imageAnalyzer
                )

                // Disable auto exposure and set manual exposure
                camera?.let { cam ->
                    val cameraControl = cam.cameraControl

                    // Disable flash
                    cameraControl.enableTorch(false)

                    // Set a fixed exposure value if the camera supports it
                    val exposureState = cam.cameraInfo.exposureState
                    if (exposureState.isExposureCompensationSupported) {
                        // Set to a middle exposure value (0) or use the current exposure level
                        val initialExposure = 0
                        cameraControl.setExposureCompensationIndex(initialExposure)
                        Log.d(TAG, "Set initial exposure compensation: $initialExposure")

                        // Store the exposure range for reference
                        Log.d(TAG, "Exposure range: ${exposureState.exposureCompensationRange.lower} to ${exposureState.exposureCompensationRange.upper}")
                    }

                    // Create a metering point at the center of the screen
                    val factory = SurfaceOrientedMeteringPointFactory(
                        previewView.width.toFloat(),
                        previewView.height.toFloat()
                    )
                    val centerPoint = factory.createPoint(0.5f, 0.5f)

                    // Create a metering action to lock focus
                    val action = FocusMeteringAction.Builder(centerPoint)
                        .build()

                    // Start the metering action
                    cameraControl.startFocusAndMetering(action)
                }

                Log.d(TAG, "Camera started successfully with auto exposure disabled")

            } catch (e: Exception) {
                Log.e(TAG, "Camera start failed", e)
            }
        }, ContextCompat.getMainExecutor(context))
    }

    /**
     * Stop the camera and release resources
     */
    fun stopCamera() {
        try {
            cameraExecutor.shutdown()
            latencyDetector?.reset()
            directYuvAnalyzer?.reset()
            Log.d(TAG, "Camera stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping camera", e)
        }
    }

    /**
     * Set a specific exposure level
     * @param exposureLevel The exposure compensation index to set (-10 to 10 typically, but depends on device)
     * @return The actual exposure level that was set, or null if exposure control is not supported
     */
    fun setExposureLevel(exposureLevel: Int): Int? {
        var actualLevel: Int? = null

        camera?.let { cam ->
            val exposureState = cam.cameraInfo.exposureState
            if (exposureState.isExposureCompensationSupported) {
                // Ensure the exposure level is within the supported range
                val clampedLevel = exposureLevel.coerceIn(
                    exposureState.exposureCompensationRange.lower,
                    exposureState.exposureCompensationRange.upper
                )

                // Set the exposure compensation
                cam.cameraControl.setExposureCompensationIndex(clampedLevel)
                Log.d(TAG, "Manual exposure set to: $clampedLevel (requested: $exposureLevel)")

                actualLevel = clampedLevel
            } else {
                Log.d(TAG, "Exposure compensation not supported on this device")
            }
        }

        return actualLevel
    }

    /**
     * Get the current exposure range supported by the device
     * @return A Pair containing the minimum and maximum exposure values, or null if not supported
     */
    fun getExposureRange(): Pair<Int, Int>? {
        camera?.let { cam ->
            val exposureState = cam.cameraInfo.exposureState
            if (exposureState.isExposureCompensationSupported) {
                return Pair(
                    exposureState.exposureCompensationRange.lower,
                    exposureState.exposureCompensationRange.upper
                )
            }
        }
        return null
    }
}
