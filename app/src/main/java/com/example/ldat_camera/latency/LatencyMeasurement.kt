package com.example.ldat_camera.latency

/**
 * Represents a single latency measurement
 */
data class LatencyMeasurement(
    val timestamp: Long,
    val luminanceChange: Float,
    val colorChange: Float,
    val isLuminanceChange: Boolean,
    val isColorChange: Boolean,
    val latencyMs: Long = 0
) {
    /**
     * Get a description of the change type
     */
    fun getChangeType(): String {
        return when {
            isLuminanceChange && isColorChange -> "Luminance & Color"
            isLuminanceChange -> "Luminance"
            isColorChange -> "Color"
            else -> "None"
        }
    }
}
