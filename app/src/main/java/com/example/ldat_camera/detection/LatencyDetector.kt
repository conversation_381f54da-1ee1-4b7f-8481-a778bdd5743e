package com.example.ldat_camera.detection

import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Rect
import android.util.Log
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import java.nio.ByteBuffer
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * Detector for measuring latency by detecting visual changes in a region of interest
 */
class LatencyDetector(
    private val listener: LatencyDetectionListener,
    private val luminanceThreshold: Float = 0.1f,
    private val colorThreshold: Float = 0.15f,
    private val skipFrames: Int = 2 // Process every nth frame
) : ImageAnalysis.Analyzer {

    interface LatencyDetectionListener {
        fun onFrameProcessed(
            timestamp: Long,
            currentLuminance: Float,
            currentColor: Int,
            luminanceChange: Float,
            colorChange: Float,
            isLuminanceChange: Boolean,
            isColorChange: Boolean,
            frameRate: Float = 0f
        )
    }

    private var lastProcessedImage: Bitmap? = null
    private var lastProcessedTimestamp: Long = 0
    private var frameCounter = 0
    private var regionOfInterest: RegionOfInterest? = null
    private val TAG = "LatencyDetector"

    // Color tracking
    private var lastDominantColor: Int = Color.BLACK

    // Luminance tracking
    private var lastAverageLuminance: Float = 0f

    /**
     * Set the region of interest for detection
     */
    fun setRegionOfInterest(roi: RegionOfInterest) {
        this.regionOfInterest = roi
        Log.d(TAG, "ROI set to: $roi")
    }

    override fun analyze(image: ImageProxy) {
        // Capture timestamp as early as possible for more accurate timing
        val currentTimestamp = System.nanoTime()

        frameCounter++

        // Only process every nth frame to reduce CPU usage
        if (frameCounter % skipFrames != 0) {
            image.close()
            return
        }

        try {
            // Use the optimized bitmap conversion
            val bitmap = image.toBitmap()

            // If we have a region of interest, crop the bitmap
            val processedBitmap = if (regionOfInterest != null && regionOfInterest!!.isValid()) {
                val roi = regionOfInterest!!
                val rect = Rect(
                    roi.left.toInt(),
                    roi.top.toInt(),
                    roi.right.toInt(),
                    roi.bottom.toInt()
                )

                // Make sure the rect is within the bitmap bounds
                val validRect = Rect(
                    max(0, rect.left),
                    max(0, rect.top),
                    min(bitmap.width, rect.right),
                    min(bitmap.height, rect.bottom)
                )

                if (validRect.width() > 0 && validRect.height() > 0) {
                    Bitmap.createBitmap(
                        bitmap,
                        validRect.left,
                        validRect.top,
                        validRect.width(),
                        validRect.height()
                    )
                } else {
                    bitmap
                }
            } else {
                bitmap
            }

            // Compare with previous frame if available
            lastProcessedImage?.let { previousImage ->
                // Detect luminance changes
                val currentLuminance = calculateAverageLuminance(processedBitmap)
                val luminanceDifference = abs(currentLuminance - lastAverageLuminance)
                val isLuminanceChange = luminanceDifference > luminanceThreshold

                // Detect color changes
                val currentDominantColor = getDominantColor(processedBitmap)
                val colorDifference = calculateColorDifference(lastDominantColor, currentDominantColor)
                val isColorChange = colorDifference > colorThreshold

                // Only log when changes are detected or every 30 frames to reduce overhead
                if (isLuminanceChange || isColorChange || frameCounter % 30 == 0) {
                    Log.d(TAG, "Luminance: $currentLuminance, Diff: $luminanceDifference, Threshold: $luminanceThreshold")
                    Log.d(TAG, "Color: ${colorToHex(currentDominantColor)}, Diff: $colorDifference, Threshold: $colorThreshold")
                }

                // Always notify the listener with current values
                listener.onFrameProcessed(
                    currentTimestamp,
                    currentLuminance,
                    currentDominantColor,
                    luminanceDifference,
                    colorDifference,
                    isLuminanceChange,
                    isColorChange
                )

                // Update tracking values
                lastAverageLuminance = currentLuminance
                lastDominantColor = currentDominantColor
            }

            // If this is the first frame, initialize the tracking values and notify listener
            if (lastProcessedImage == null) {
                val initialLuminance = calculateAverageLuminance(processedBitmap)
                val initialColor = getDominantColor(processedBitmap)
                lastAverageLuminance = initialLuminance
                lastDominantColor = initialColor
                Log.d(TAG, "Initial luminance: $initialLuminance, color: ${colorToHex(initialColor)}")

                // Notify with initial values
                listener.onFrameProcessed(
                    currentTimestamp,
                    initialLuminance,
                    initialColor,
                    0f, // No change yet
                    0f, // No change yet
                    false,
                    false
                )
            }

            // Update reference frame and timestamp
            lastProcessedImage?.recycle()
            lastProcessedImage = processedBitmap
            lastProcessedTimestamp = currentTimestamp

        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing image", e)
        } finally {
            image.close()
        }
    }

    /**
     * Calculate the average luminance of an image (0-1 range)
     * Optimized version that samples pixels for faster processing
     */
    private fun calculateAverageLuminance(bitmap: Bitmap): Float {
        val width = bitmap.width
        val height = bitmap.height

        // Sample size - only process every Nth pixel
        // Higher values = faster but less accurate
        val sampleSize = 8  // Increased from 4 to 8 for faster processing

        // Calculate how many pixels we'll sample
        val sampledWidth = width / sampleSize
        val sampledHeight = height / sampleSize
        val sampleCount = sampledWidth * sampledHeight

        // If the bitmap is very small, process all pixels
        if (sampleCount < 100) {
            return calculateFullLuminance(bitmap)
        }

        var totalLuminance = 0L
        val pixels = IntArray(sampleCount)

        // Sample pixels in a grid pattern
        for (y in 0 until sampledHeight) {
            for (x in 0 until sampledWidth) {
                val pixelX = x * sampleSize
                val pixelY = y * sampleSize
                if (pixelX < width && pixelY < height) {
                    val pixel = bitmap.getPixel(pixelX, pixelY)
                    val r = Color.red(pixel)
                    val g = Color.green(pixel)
                    val b = Color.blue(pixel)

                    // Standard luminance formula
                    val luminance = (0.299 * r + 0.587 * g + 0.114 * b).toInt()
                    totalLuminance += luminance
                }
            }
        }

        return totalLuminance.toFloat() / (sampleCount * 255f)
    }

    /**
     * Calculate luminance using all pixels (slower but more accurate)
     * Used as fallback for small images
     */
    private fun calculateFullLuminance(bitmap: Bitmap): Float {
        var totalLuminance = 0L
        val width = bitmap.width
        val height = bitmap.height
        val pixels = IntArray(width * height)

        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

        for (pixel in pixels) {
            val r = Color.red(pixel)
            val g = Color.green(pixel)
            val b = Color.blue(pixel)

            // Standard luminance formula
            val luminance = (0.299 * r + 0.587 * g + 0.114 * b).toInt()
            totalLuminance += luminance
        }

        return totalLuminance.toFloat() / (pixels.size * 255f)
    }

    /**
     * Get the dominant color in the image
     * Optimized version that samples pixels for faster processing
     */
    private fun getDominantColor(bitmap: Bitmap): Int {
        val width = bitmap.width
        val height = bitmap.height

        // Sample size - only process every Nth pixel
        // Higher values = faster but less accurate
        val sampleSize = 8  // Increased from 4 to 8 for faster processing

        // Calculate how many pixels we'll sample
        val sampledWidth = width / sampleSize
        val sampledHeight = height / sampleSize
        val sampleCount = sampledWidth * sampledHeight

        // If the bitmap is very small, process all pixels
        if (sampleCount < 100) {
            return getFullDominantColor(bitmap)
        }

        // Simple approach: average the colors
        var totalR = 0L
        var totalG = 0L
        var totalB = 0L
        var pixelCount = 0

        // Sample pixels in a grid pattern
        for (y in 0 until sampledHeight) {
            for (x in 0 until sampledWidth) {
                val pixelX = x * sampleSize
                val pixelY = y * sampleSize
                if (pixelX < width && pixelY < height) {
                    val pixel = bitmap.getPixel(pixelX, pixelY)
                    totalR += Color.red(pixel)
                    totalG += Color.green(pixel)
                    totalB += Color.blue(pixel)
                    pixelCount++
                }
            }
        }

        if (pixelCount == 0) return Color.BLACK

        val avgR = (totalR / pixelCount).toInt()
        val avgG = (totalG / pixelCount).toInt()
        val avgB = (totalB / pixelCount).toInt()

        return Color.rgb(avgR, avgG, avgB)
    }

    /**
     * Get dominant color using all pixels (slower but more accurate)
     * Used as fallback for small images
     */
    private fun getFullDominantColor(bitmap: Bitmap): Int {
        val width = bitmap.width
        val height = bitmap.height
        val pixels = IntArray(width * height)

        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

        // Simple approach: average the colors
        var totalR = 0L
        var totalG = 0L
        var totalB = 0L

        for (pixel in pixels) {
            totalR += Color.red(pixel)
            totalG += Color.green(pixel)
            totalB += Color.blue(pixel)
        }

        val avgR = (totalR / pixels.size).toInt()
        val avgG = (totalG / pixels.size).toInt()
        val avgB = (totalB / pixels.size).toInt()

        return Color.rgb(avgR, avgG, avgB)
    }

    /**
     * Calculate the difference between two colors (0-1 range)
     */
    private fun calculateColorDifference(color1: Int, color2: Int): Float {
        val r1 = Color.red(color1)
        val g1 = Color.green(color1)
        val b1 = Color.blue(color1)

        val r2 = Color.red(color2)
        val g2 = Color.green(color2)
        val b2 = Color.blue(color2)

        // Euclidean distance in RGB space, normalized to 0-1 range
        val distance = Math.sqrt(
            Math.pow((r1 - r2).toDouble(), 2.0) +
            Math.pow((g1 - g2).toDouble(), 2.0) +
            Math.pow((b1 - b2).toDouble(), 2.0)
        )

        return (distance / Math.sqrt(3.0 * 255.0 * 255.0)).toFloat()
    }

    /**
     * Convert a color to hex string for debugging
     */
    private fun colorToHex(color: Int): String {
        return String.format(
            "#%02X%02X%02X",
            Color.red(color),
            Color.green(color),
            Color.blue(color)
        )
    }

    /**
     * Convert ImageProxy to Bitmap with optimized quality/speed tradeoff
     */
    private fun ImageProxy.toBitmap(): Bitmap {
        val yBuffer = planes[0].buffer // Y
        val uBuffer = planes[1].buffer // U
        val vBuffer = planes[2].buffer // V

        val ySize = yBuffer.remaining()
        val uSize = uBuffer.remaining()
        val vSize = vBuffer.remaining()

        val nv21 = ByteArray(ySize + uSize + vSize)

        // U and V are swapped
        yBuffer.get(nv21, 0, ySize)
        vBuffer.get(nv21, ySize, vSize)
        uBuffer.get(nv21, ySize + vSize, uSize)

        val yuvImage = android.graphics.YuvImage(nv21, android.graphics.ImageFormat.NV21, width, height, null)
        val out = java.io.ByteArrayOutputStream()

        // Use even lower JPEG quality (50 instead of 70) for faster processing
        // This is still sufficient for detecting brightness and color changes
        yuvImage.compressToJpeg(android.graphics.Rect(0, 0, width, height), 50, out)
        val imageBytes = out.toByteArray()

        // Use inSampleSize to further reduce processing time
        val options = android.graphics.BitmapFactory.Options().apply {
            // Always use downsampling for faster processing
            inSampleSize = if (width > 1000 || height > 1000) 4 else 2

            // Use RGB_565 instead of ARGB_8888 to reduce memory usage and processing time
            inPreferredConfig = android.graphics.Bitmap.Config.RGB_565
        }

        return android.graphics.BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size, options)
    }

    /**
     * Reset the detector state
     */
    fun reset() {
        lastProcessedImage?.recycle()
        lastProcessedImage = null
        lastProcessedTimestamp = 0
        frameCounter = 0
        lastAverageLuminance = 0f
        lastDominantColor = Color.BLACK
    }
}
