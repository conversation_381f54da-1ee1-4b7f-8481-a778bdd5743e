package com.example.ldat_camera.detection

import android.graphics.RectF

/**
 * Represents a region of interest on the screen for latency detection
 */
data class RegionOfInterest(
    var left: Float,
    var top: Float,
    var right: Float,
    var bottom: Float
) {
    /**
     * Convert to RectF for drawing and calculations
     */
    fun toRectF(): RectF {
        return RectF(left, top, right, bottom)
    }
    
    /**
     * Check if the ROI is valid (has positive width and height)
     */
    fun isValid(): <PERSON><PERSON>an {
        return right > left && bottom > top
    }
    
    /**
     * Get the width of the ROI
     */
    fun width(): Float {
        return right - left
    }
    
    /**
     * Get the height of the ROI
     */
    fun height(): Float {
        return bottom - top
    }
    
    companion object {
        /**
         * Create a default ROI that covers the center 25% of the screen
         */
        fun createDefault(screenWidth: Int, screenHeight: Int): RegionOfInterest {
            val centerX = screenWidth / 2f
            val centerY = screenHeight / 2f
            val width = screenWidth / 4f
            val height = screenHeight / 4f
            
            return RegionOfInterest(
                centerX - width / 2,
                centerY - height / 2,
                centerX + width / 2,
                centerY + height / 2
            )
        }
    }
}
