package com.example.ldat_camera.detection

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.ImageFormat
import android.graphics.Rect
import android.graphics.YuvImage
import android.util.Log
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer
import kotlin.math.abs

/**
 * Utility class for detecting changes between camera frames
 * to identify screen changes on a PC monitor
 */
class ScreenChangeDetector(
    private val listener: ScreenChangeListener,
    private val sensitivityThreshold: Float = 0.05f, // Default 5% change threshold
    private val skipFrames: Int = 10, // Process every 10th frame by default
    private val brightnessThreshold: Float = 0.15f // Default 15% brightness change threshold
) : ImageAnalysis.Analyzer {

    interface ScreenChangeListener {
        fun onScreenChangeDetected(changePercentage: Float, brightnessChange: Float = 0f, isBrightnessChange: Boolean = false)
    }

    private var lastProcessedImage: ByteArray? = null
    private var lastAverageBrightness: Float = 0f
    private var frameCounter = 0
    private val TAG = "ScreenChangeDetector"

    override fun analyze(image: ImageProxy) {
        frameCounter++

        // Only process every nth frame to reduce CPU usage
        if (frameCounter % skipFrames != 0) {
            image.close()
            return
        }

        try {
            val bitmap = image.toBitmap()
            val currentImageData = bitmap.toByteArray()

            // Calculate current frame brightness
            val currentBrightness = calculateAverageBrightness(currentImageData)

            // Compare with previous frame if available
            lastProcessedImage?.let { previousImageData ->
                // Check for content changes (pixel differences)
                val changePercentage = calculateDifference(previousImageData, currentImageData)

                // Check for brightness changes
                val brightnessDifference = abs(currentBrightness - lastAverageBrightness)
                val isBrightnessChange = brightnessDifference > brightnessThreshold

                // Log the brightness values for debugging
                Log.d(TAG, "Current brightness: $currentBrightness, Previous: $lastAverageBrightness, Difference: $brightnessDifference")

                // Detect either content changes or brightness changes
                if (changePercentage > sensitivityThreshold || isBrightnessChange) {
                    if (isBrightnessChange) {
                        Log.d(TAG, "Brightness change detected: $brightnessDifference (${brightnessDifference * 100}%)")
                        listener.onScreenChangeDetected(changePercentage, brightnessDifference, true)
                    } else {
                        Log.d(TAG, "Content change detected: $changePercentage")
                        listener.onScreenChangeDetected(changePercentage)
                    }
                }
            }

            // Update reference frame and brightness
            lastProcessedImage = currentImageData
            lastAverageBrightness = currentBrightness
            bitmap.recycle()
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing image", e)
        } finally {
            image.close()
        }
    }

    /**
     * Calculate the percentage difference between two images
     */
    private fun calculateDifference(previous: ByteArray, current: ByteArray): Float {
        if (previous.size != current.size) {
            Log.e(TAG, "Image size mismatch: ${previous.size} vs ${current.size}")
            return 0f
        }

        var differentPixels = 0
        val pixelCount = previous.size

        for (i in previous.indices) {
            if (abs(previous[i] - current[i]) > 10) { // Tolerance for minor variations
                differentPixels++
            }
        }

        return differentPixels.toFloat() / pixelCount
    }

    /**
     * Calculate the average brightness of an image
     * Returns a value between 0 (completely black) and 1 (completely white)
     */
    private fun calculateAverageBrightness(imageData: ByteArray): Float {
        if (imageData.isEmpty()) return 0f

        var totalBrightness = 0L
        for (byte in imageData) {
            // Convert signed byte to unsigned (0-255)
            totalBrightness += (byte.toInt() and 0xFF)
        }

        // Return average brightness normalized to 0-1 range
        return (totalBrightness.toFloat() / (imageData.size * 255f))
    }

    /**
     * Reset the detector state
     */
    fun reset() {
        lastProcessedImage = null
        lastAverageBrightness = 0f
        frameCounter = 0
    }

    /**
     * Convert ImageProxy to Bitmap
     */
    private fun ImageProxy.toBitmap(): Bitmap {
        val yBuffer = planes[0].buffer // Y
        val uBuffer = planes[1].buffer // U
        val vBuffer = planes[2].buffer // V

        val ySize = yBuffer.remaining()
        val uSize = uBuffer.remaining()
        val vSize = vBuffer.remaining()

        val nv21 = ByteArray(ySize + uSize + vSize)

        // U and V are swapped
        yBuffer.get(nv21, 0, ySize)
        vBuffer.get(nv21, ySize, vSize)
        uBuffer.get(nv21, ySize + vSize, uSize)

        val yuvImage = YuvImage(nv21, ImageFormat.NV21, width, height, null)
        val out = ByteArrayOutputStream()
        yuvImage.compressToJpeg(Rect(0, 0, width, height), 100, out)
        val imageBytes = out.toByteArray()

        // Create a downsampled bitmap to reduce processing overhead
        val originalBitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)
        val scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, 160, 120, true)
        originalBitmap.recycle()

        return scaledBitmap
    }

    /**
     * Convert Bitmap to ByteArray for comparison
     */
    private fun Bitmap.toByteArray(): ByteArray {
        // Convert to grayscale for simpler comparison
        val pixels = IntArray(width * height)
        getPixels(pixels, 0, width, 0, 0, width, height)

        val result = ByteArray(width * height)

        for (i in pixels.indices) {
            val pixel = pixels[i]
            // Convert RGB to grayscale using standard luminance formula
            val gray = (0.299 * ((pixel shr 16) and 0xFF) +
                       0.587 * ((pixel shr 8) and 0xFF) +
                       0.114 * (pixel and 0xFF)).toInt()
            result[i] = gray.toByte()
        }

        return result
    }
}
