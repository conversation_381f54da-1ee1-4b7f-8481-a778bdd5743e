package com.example.ldat_camera.detection

import android.graphics.Color
import android.util.Log
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import java.nio.ByteBuffer
import kotlin.math.abs

/**
 * Analyzes camera frames directly in YUV format without bitmap conversion
 * This is much more efficient than converting to bitmap first
 */
class DirectYuvAnalyzer(
    private val listener: LatencyDetectionListener,
    private val luminanceThreshold: Float = 0.015f,
    private val colorThreshold: Float = 0.02f,
    private val skipFrames: Int = 1
) : ImageAnalysis.Analyzer {

    companion object {
        private const val TAG = "DirectYuvAnalyzer"
    }

    // Interface for latency detection callbacks
    interface LatencyDetectionListener {
        fun onFrameProcessed(
            timestamp: Long,
            luminance: Float,
            dominantColor: Int,
            luminanceDiff: Float,
            colorDiff: Float,
            isLuminanceChange: Boolean,
            isColorChange: Boolean,
            frameRate: Float = 0f
        )
    }

    // State tracking
    private var frameCounter = 0
    private var lastProcessedTimestamp = 0L
    private var lastAverageLuminance = 0f
    private var lastAverageR = 0
    private var lastAverageG = 0
    private var lastAverageB = 0
    private var isFirstFrame = true

    // Frame rate tracking
    private var frameRateCalculationStartTime = 0L
    private var frameCountForRate = 0
    private var currentFrameRate = 0f
    private val FRAME_RATE_CALCULATION_INTERVAL_MS = 1000 // Calculate FPS every second

    override fun analyze(image: ImageProxy) {
        // Capture timestamp as early as possible for more accurate timing
        val currentTimestamp = System.nanoTime()

        frameCounter++

        // Calculate frame rate
        calculateFrameRate()

        // Only process every nth frame to reduce CPU usage
        if (frameCounter % skipFrames != 0) {
            image.close()
            return
        }

        try {
            // Get the Y plane (luminance)
            val yBuffer = image.planes[0].buffer
            val ySize = yBuffer.remaining()

            // Get U and V planes (color information)
            val uBuffer = image.planes[1].buffer
            val vBuffer = image.planes[2].buffer

            // Calculate average luminance directly from Y plane
            val currentLuminance = calculateAverageLuminance(yBuffer, ySize)

            // Calculate average RGB values from YUV data
            val (avgR, avgG, avgB) = calculateAverageRgb(yBuffer, uBuffer, vBuffer, image.width, image.height)
            val currentDominantColor = Color.rgb(avgR, avgG, avgB)

            // For the first frame, just store the values
            if (isFirstFrame) {
                lastAverageLuminance = currentLuminance
                lastAverageR = avgR
                lastAverageG = avgG
                lastAverageB = avgB
                isFirstFrame = false

                // Notify with initial values
                listener.onFrameProcessed(
                    currentTimestamp,
                    currentLuminance,
                    currentDominantColor,
                    0f, // No change yet
                    0f, // No change yet
                    false,
                    false,
                    currentFrameRate
                )
            } else {
                // Calculate differences
                val luminanceDifference = abs(currentLuminance - lastAverageLuminance)
                val isLuminanceChange = luminanceDifference > luminanceThreshold

                // Calculate color difference using Euclidean distance
                val colorDifference = calculateColorDifference(
                    lastAverageR, lastAverageG, lastAverageB,
                    avgR, avgG, avgB
                )
                val isColorChange = colorDifference > colorThreshold

                // Only log when changes are detected or every 30 frames
                if (isLuminanceChange || isColorChange || frameCounter % 30 == 0) {
                    Log.d(TAG, "Luminance: $currentLuminance, Diff: $luminanceDifference, Threshold: $luminanceThreshold")
                    Log.d(TAG, "Color: R=$avgR,G=$avgG,B=$avgB, Diff: $colorDifference, Threshold: $colorThreshold")
                }

                // Notify listener
                listener.onFrameProcessed(
                    currentTimestamp,
                    currentLuminance,
                    currentDominantColor,
                    luminanceDifference,
                    colorDifference,
                    isLuminanceChange,
                    isColorChange,
                    currentFrameRate
                )

                // Update tracking values
                lastAverageLuminance = currentLuminance
                lastAverageR = avgR
                lastAverageG = avgG
                lastAverageB = avgB
            }

            lastProcessedTimestamp = currentTimestamp

        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing YUV image", e)
        } finally {
            image.close()
        }
    }

    /**
     * Calculate average luminance directly from Y plane
     * Y values are already luminance values in the range 0-255
     */
    private fun calculateAverageLuminance(yBuffer: ByteBuffer, ySize: Int): Float {
        // Sample size - only process a subset of pixels for efficiency
        val sampleSize = 16

        var totalY = 0L
        var sampledPixels = 0

        // Reset buffer position
        val originalPosition = yBuffer.position()

        // Sample Y values
        for (i in 0 until ySize step sampleSize) {
            if (i < ySize) {
                // Y values are unsigned bytes, so convert to 0-255 range
                val y = yBuffer.get(i).toInt() and 0xFF
                totalY += y
                sampledPixels++
            }
        }

        // Restore buffer position
        yBuffer.position(originalPosition)

        // Normalize to 0-1 range
        return if (sampledPixels > 0) {
            (totalY.toFloat() / sampledPixels) / 255f
        } else {
            0f
        }
    }

    /**
     * Calculate average RGB values directly from YUV data
     * This avoids the expensive bitmap conversion
     */
    private fun calculateAverageRgb(
        yBuffer: ByteBuffer,
        uBuffer: ByteBuffer,
        vBuffer: ByteBuffer,
        width: Int,
        height: Int
    ): Triple<Int, Int, Int> {
        // Sample size - only process a subset of pixels for efficiency
        val sampleSize = 16

        var totalR = 0L
        var totalG = 0L
        var totalB = 0L
        var sampledPixels = 0

        // Reset buffer positions
        val yOriginalPosition = yBuffer.position()
        val uOriginalPosition = uBuffer.position()
        val vOriginalPosition = vBuffer.position()

        // YUV420 format has one Y value per pixel, but U and V are shared among 4 pixels
        // So we need to sample Y values and corresponding U,V values
        val yRowStride = width
        val uvRowStride = width / 2
        val uvPixelStride = 2

        for (y in 0 until height step sampleSize) {
            for (x in 0 until width step sampleSize) {
                val yIndex = y * yRowStride + x

                // Make sure we don't go out of bounds
                if (yIndex >= yBuffer.capacity()) continue

                // Get Y value (luminance)
                val yValue = yBuffer.get(yIndex).toInt() and 0xFF

                // Calculate U,V indices
                val uvRow = y / 2
                val uvCol = x / 2
                val uvIndex = uvRow * uvRowStride + uvCol * uvPixelStride

                // Make sure we don't go out of bounds
                if (uvIndex >= uBuffer.capacity() || uvIndex >= vBuffer.capacity()) continue

                // Get U,V values
                val uValue = (uBuffer.get(uvIndex).toInt() and 0xFF) - 128
                val vValue = (vBuffer.get(uvIndex).toInt() and 0xFF) - 128

                // Convert YUV to RGB using standard conversion formula
                val r = yValue + (1.370705f * vValue)
                val g = yValue - (0.698001f * vValue) - (0.337633f * uValue)
                val b = yValue + (1.732446f * uValue)

                // Clamp to 0-255 range
                totalR += r.coerceIn(0f, 255f).toInt()
                totalG += g.coerceIn(0f, 255f).toInt()
                totalB += b.coerceIn(0f, 255f).toInt()

                sampledPixels++
            }
        }

        // Restore buffer positions
        yBuffer.position(yOriginalPosition)
        uBuffer.position(uOriginalPosition)
        vBuffer.position(vOriginalPosition)

        // Calculate averages
        return if (sampledPixels > 0) {
            Triple(
                (totalR / sampledPixels).toInt(),
                (totalG / sampledPixels).toInt(),
                (totalB / sampledPixels).toInt()
            )
        } else {
            Triple(0, 0, 0)
        }
    }

    /**
     * Calculate color difference using Euclidean distance in RGB space
     */
    private fun calculateColorDifference(
        r1: Int, g1: Int, b1: Int,
        r2: Int, g2: Int, b2: Int
    ): Float {
        val distance = Math.sqrt(
            Math.pow((r1 - r2).toDouble(), 2.0) +
            Math.pow((g1 - g2).toDouble(), 2.0) +
            Math.pow((b1 - b2).toDouble(), 2.0)
        )

        // Normalize to 0-1 range
        return (distance / Math.sqrt(3.0 * 255.0 * 255.0)).toFloat()
    }

    /**
     * Calculate the current frame rate
     */
    private fun calculateFrameRate() {
        val currentTimeMs = System.currentTimeMillis()

        // Initialize start time if this is the first frame
        if (frameRateCalculationStartTime == 0L) {
            frameRateCalculationStartTime = currentTimeMs
            frameCountForRate = 1
            return
        }

        // Count frames
        frameCountForRate++

        // Calculate FPS every second
        val elapsedMs = currentTimeMs - frameRateCalculationStartTime
        if (elapsedMs >= FRAME_RATE_CALCULATION_INTERVAL_MS) {
            // Calculate frames per second
            currentFrameRate = (frameCountForRate * 1000f) / elapsedMs

            // Log the frame rate
            Log.d(TAG, "Current frame rate: $currentFrameRate fps")

            // Reset counters for next calculation
            frameRateCalculationStartTime = currentTimeMs
            frameCountForRate = 0
        }
    }

    /**
     * Get the current frame rate
     */
    fun getCurrentFrameRate(): Float {
        return currentFrameRate
    }

    /**
     * Reset the analyzer state
     */
    fun reset() {
        frameCounter = 0
        lastProcessedTimestamp = 0
        lastAverageLuminance = 0f
        lastAverageR = 0
        lastAverageG = 0
        lastAverageB = 0
        isFirstFrame = true
        frameRateCalculationStartTime = 0
        frameCountForRate = 0
        currentFrameRate = 0f
    }
}
