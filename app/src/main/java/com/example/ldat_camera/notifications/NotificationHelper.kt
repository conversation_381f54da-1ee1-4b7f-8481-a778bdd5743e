package com.example.ldat_camera.notifications

import android.Manifest
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import com.example.ldat_camera.R

/**
 * Helper class to manage notifications for screen change detection
 */
class NotificationHelper(private val context: Context) {

    companion object {
        private const val CHANNEL_ID = "screen_change_channel"
        private const val NOTIFICATION_ID = 1001
        private const val TAG = "NotificationHelper"
    }

    init {
        createNotificationChannel()
    }

    /**
     * Create the notification channel for Android O and above
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Screen Change Notifications"
            val descriptionText = "Notifications for detected screen changes"
            val importance = NotificationManager.IMPORTANCE_DEFAULT
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * Check if notification permission is granted
     */
    fun hasNotificationPermission(): Boolean {
        // For Android 13+ (TIRAMISU), check POST_NOTIFICATIONS permission
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // For older versions, notification permissions were granted by default
            true
        }
    }

    /**
     * Show a notification when a screen change is detected
     */
    fun showScreenChangeNotification(
        changePercentage: Float,
        changeType: String = "Content",
        changeCount: Int = 0
    ) {
        val formattedPercentage = String.format("%.1f", changePercentage * 100)

        // Check if we have notification permission
        if (!hasNotificationPermission()) {
            Log.d(TAG, "Cannot show notification: Permission not granted")
            return
        }

        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("Screen Change Detected (#$changeCount)")
            .setContentText("$changeType change of $formattedPercentage% was detected")
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)

        try {
            with(NotificationManagerCompat.from(context)) {
                notify(NOTIFICATION_ID, builder.build())
                Log.d(TAG, "Notification sent: $changeType change of $formattedPercentage% detected (Count: $changeCount)")
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "Error showing notification", e)
        }
    }
}
