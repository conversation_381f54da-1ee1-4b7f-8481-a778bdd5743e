package com.example.ldat_camera.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import androidx.core.content.ContextCompat
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.sqrt

/**
 * Detector for mouse click sounds using the device's microphone.
 * This class listens for audio input and detects when a mouse click sound occurs.
 */
class AudioClickDetector(
    private val coroutineScope: CoroutineScope,
    private val context: Context? = null
) {

    companion object {
        private const val TAG = "AudioClickDetector"

        // Threshold for detecting a click (lower value = more sensitive)
        private const val AMPLITUDE_THRESHOLD = 500f

        // Peak amplitude threshold (for alternative detection method)
        private const val PEAK_AMPLITUDE_THRESHOLD = 5000

        // Cooldown period between click detections to prevent multiple detections of the same click
        private const val CLICK_COOLDOWN_MS = 300L

        // Frequency characteristics of mouse clicks (typically high-frequency)
        private const val MIN_HIGH_FREQUENCY_RATIO = 0.3f // Minimum ratio of high-frequency content for mouse clicks
    }

    // Audio recording configuration
    private val sampleRate = 44100
    private val channelConfig = AudioFormat.CHANNEL_IN_MONO
    private val audioFormat = AudioFormat.ENCODING_PCM_16BIT

    // Buffer size for audio recording
    private val bufferSize = AudioRecord.getMinBufferSize(
        sampleRate, channelConfig, audioFormat
    )

    // AudioRecord instance for capturing audio
    private var audioRecord: AudioRecord? = null

    // Job for the recording coroutine
    private var recordingJob: Job? = null

    // Flag to track if recording is active
    private var isRecording = false

    // Timestamp of the last detected click
    private var lastClickTime = 0L

    // Counter for detected clicks
    private var clickCount = 0

    // Callback for when a click is detected
    var onClickDetected: ((Long) -> Unit)? = null

    /**
     * Start listening for mouse click sounds
     * @return true if started successfully, false otherwise
     */
    fun startListening(): Boolean {
        if (isRecording) return true

        // Check for audio recording permission
        if (context != null) {
            val hasPermission = ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED

            if (!hasPermission) {
                Log.e(TAG, "Missing RECORD_AUDIO permission")
                return false
            }
        }

        try {
            // Initialize AudioRecord with permission check
            @Suppress("MissingPermission")
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                sampleRate,
                channelConfig,
                audioFormat,
                bufferSize
            )

            // Check if initialization was successful
            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                Log.e(TAG, "AudioRecord initialization failed")
                return false
            }

            // Start recording
            audioRecord?.startRecording()
            isRecording = true

            // Process audio in a coroutine
            recordingJob = coroutineScope.launch {
                processAudio()
            }

            Log.d(TAG, "Started listening for mouse clicks")
            return true
        } catch (e: SecurityException) {
            Log.e(TAG, "Security exception: Missing RECORD_AUDIO permission: ${e.message}")
            stopListening()
            return false
        } catch (e: Exception) {
            Log.e(TAG, "Error starting audio recording: ${e.message}")
            stopListening()
            return false
        }
    }

    /**
     * Stop listening for mouse click sounds
     */
    fun stopListening() {
        isRecording = false
        recordingJob?.cancel()

        try {
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null
            Log.d(TAG, "Stopped listening for mouse clicks")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping audio recording: ${e.message}")
        }
    }

    /**
     * Process audio data to detect mouse clicks
     */
    private suspend fun processAudio() {
        val buffer = ShortArray(bufferSize)

        withContext(Dispatchers.IO) {
            while (isRecording) {
                val readResult = audioRecord?.read(buffer, 0, bufferSize) ?: -1

                if (readResult > 0) {
                    // Calculate RMS (Root Mean Square) of the audio sample
                    val rms = calculateRMS(buffer, readResult)

                    // Find peak amplitude (alternative detection method)
                    val peakAmplitude = findPeakAmplitude(buffer, readResult)

                    // Analyze frequency content to distinguish mouse clicks from other sounds
                    val highFreqRatio = analyzeFrequencyContent(buffer, readResult)

                    // Log high audio levels for debugging
                    if (rms > AMPLITUDE_THRESHOLD / 2 || peakAmplitude > PEAK_AMPLITUDE_THRESHOLD / 2) {
                        Log.d(TAG, "High audio level detected: RMS = $rms, Peak = $peakAmplitude, HighFreqRatio = $highFreqRatio")
                    }

                    val currentTime = System.currentTimeMillis()
                    val cooldownPassed = currentTime - lastClickTime > CLICK_COOLDOWN_MS

                    // Check if the audio has characteristics of a mouse click
                    val isClickDetected = isLikelyMouseClick(rms, peakAmplitude, highFreqRatio) && cooldownPassed

                    if (isClickDetected) {
                        lastClickTime = currentTime
                        clickCount++

                        // Notify listeners on the main thread
                        withContext(Dispatchers.Main) {
                            onClickDetected?.invoke(currentTime)
                            Log.d(TAG, "Mouse click detected! Count: $clickCount, RMS: $rms, Peak: $peakAmplitude, HighFreqRatio: $highFreqRatio")
                        }
                    }
                }
            }
        }
    }

    /**
     * Calculate the Root Mean Square (RMS) of an audio sample
     * RMS is a measure of the average power of the audio signal
     */
    private fun calculateRMS(buffer: ShortArray, readSize: Int): Float {
        var sum = 0f
        for (i in 0 until readSize) {
            sum += buffer[i] * buffer[i]
        }
        val mean = sum / readSize
        return sqrt(mean)
    }

    /**
     * Find the peak amplitude in the audio sample
     * This can be more effective for detecting sharp sounds like clicks
     */
    private fun findPeakAmplitude(buffer: ShortArray, readSize: Int): Int {
        var peak = 0
        for (i in 0 until readSize) {
            val abs = kotlin.math.abs(buffer[i].toInt())
            if (abs > peak) {
                peak = abs
            }
        }
        return peak
    }

    /**
     * Analyze the frequency content of the audio sample
     * Mouse clicks typically have more high-frequency content
     * @return The ratio of high-frequency energy to total energy
     */
    private fun analyzeFrequencyContent(buffer: ShortArray, readSize: Int): Float {
        var totalEnergy = 0f
        var highFreqEnergy = 0f

        // Simple frequency analysis by comparing adjacent samples
        // High frequency content causes more rapid changes between samples
        for (i in 1 until readSize) {
            val diff = kotlin.math.abs(buffer[i] - buffer[i-1])
            val energy = diff * diff

            totalEnergy += energy

            // Consider changes above a certain rate as high frequency
            if (diff > 50) {
                highFreqEnergy += energy
            }
        }

        return if (totalEnergy > 0) highFreqEnergy / totalEnergy else 0f
    }

    /**
     * Determine if the audio sample has characteristics of a mouse click
     * This checks both amplitude and frequency characteristics
     */
    private fun isLikelyMouseClick(
        rms: Float,
        peakAmplitude: Int,
        highFreqRatio: Float
    ): Boolean {
        // Check if amplitude exceeds threshold
        val amplitudeCheck = rms > AMPLITUDE_THRESHOLD || peakAmplitude > PEAK_AMPLITUDE_THRESHOLD

        // Check if frequency content matches mouse click profile
        val frequencyCheck = highFreqRatio >= MIN_HIGH_FREQUENCY_RATIO

        // Log detailed information for debugging
        if (amplitudeCheck) {
            Log.d(TAG, "Amplitude check passed: RMS=$rms, Peak=$peakAmplitude, HighFreqRatio=$highFreqRatio")
        }

        // Both checks must pass
        return amplitudeCheck && frequencyCheck
    }

    /**
     * Get the current click count
     */
    fun getClickCount(): Int = clickCount

    /**
     * Reset the click counter to zero
     */
    fun resetClickCount() {
        clickCount = 0
    }
}
