package com.example.ldat_camera

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.example.ldat_camera.ui.screens.SimpleBrightnessDetectionScreen
import com.example.ldat_camera.ui.theme.Ldat_cameraTheme

/**
 * Main activity for the Brightness and Color Detection app
 *
 * This app uses the device camera to detect and display brightness and color values
 * from the camera feed, which can be used for LDAT-style latency measurement.
 */
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Ldat_cameraTheme {
                SimpleBrightnessDetectionScreen()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Ensure any camera resources are properly released
    }
}