package com.example.ldat_camera.camera

import android.content.Context
import android.util.Log
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.example.ldat_camera.detection.ScreenChangeDetector
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * Manages camera operations for the screen change detection app
 */
class CameraManager(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val previewView: PreviewView,
    private val screenChangeListener: ScreenChangeDetector.ScreenChangeListener
) {
    private var camera: Camera? = null
    private var cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()
    private var imageAnalyzer: ImageAnalysis? = null
    private var screenChangeDetector: ScreenChangeDetector? = null

    private val TAG = "CameraManager"

    /**
     * Start the camera with screen change detection
     */
    fun startCamera(
        sensitivityThreshold: Float = 0.05f,
        skipFrames: Int = 10,
        brightnessThreshold: Float = 0.15f
    ) {
        Log.d(TAG, "Starting camera with sensitivity: $sensitivityThreshold, skipFrames: $skipFrames")

        if (previewView == null) {
            Log.e(TAG, "PreviewView is null, cannot start camera")
            return
        }

        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        Log.d(TAG, "Camera provider future created")

        cameraProviderFuture.addListener({
            try {
                Log.d(TAG, "Getting camera provider")
                val cameraProvider = cameraProviderFuture.get()

                // Set up the preview use case
                Log.d(TAG, "Setting up preview")
                val preview = Preview.Builder()
                    .build()
                    .also {
                        Log.d(TAG, "Setting surface provider")
                        it.setSurfaceProvider(previewView.surfaceProvider)
                    }

                // Set up the image analyzer
                Log.d(TAG, "Setting up image analyzer")
                imageAnalyzer = ImageAnalysis.Builder()
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .build()

                // Create and attach the screen change detector
                Log.d(TAG, "Creating screen change detector with sensitivity: $sensitivityThreshold, skipFrames: $skipFrames, brightnessThreshold: $brightnessThreshold")
                screenChangeDetector = ScreenChangeDetector(
                    screenChangeListener,
                    sensitivityThreshold,
                    skipFrames,
                    brightnessThreshold
                )

                Log.d(TAG, "Setting analyzer")
                imageAnalyzer?.setAnalyzer(cameraExecutor, screenChangeDetector!!)

                // Select back camera as a default
                Log.d(TAG, "Selecting camera")
                val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

                // Unbind any bound use cases before rebinding
                Log.d(TAG, "Unbinding previous use cases")
                cameraProvider.unbindAll()

                // Bind use cases to camera
                Log.d(TAG, "Binding use cases to lifecycle")
                camera = cameraProvider.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    preview,
                    imageAnalyzer
                )

                Log.d(TAG, "Camera started successfully")

            } catch (e: Exception) {
                Log.e(TAG, "Camera start failed", e)
            }
        }, ContextCompat.getMainExecutor(context))
    }

    /**
     * Stop the camera and release resources
     */
    fun stopCamera() {
        try {
            Log.d(TAG, "Stopping camera")

            // Shutdown the executor
            if (!cameraExecutor.isShutdown) {
                Log.d(TAG, "Shutting down camera executor")
                cameraExecutor.shutdown()
            }

            // Reset the detector
            if (screenChangeDetector != null) {
                Log.d(TAG, "Resetting screen change detector")
                screenChangeDetector?.reset()
            }

            Log.d(TAG, "Camera stopped successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping camera", e)
        }
    }

    /**
     * Update detection parameters
     */
    fun updateParameters(
        sensitivityThreshold: Float,
        skipFrames: Int,
        brightnessThreshold: Float = 0.15f
    ) {
        stopCamera()
        startCamera(sensitivityThreshold, skipFrames, brightnessThreshold)
    }
}
