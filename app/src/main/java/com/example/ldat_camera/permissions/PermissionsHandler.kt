package com.example.ldat_camera.permissions

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionState
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale

/**
 * Utility class to handle permission requests
 */
object PermissionsHandler {

    /**
     * Check if camera permission is granted
     */
    fun hasCameraPermission(context: Context): <PERSON><PERSON>an {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * Get all required permissions based on Android version
     */
    fun getRequiredPermissions(): List<String> {
        val permissions = mutableListOf(
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO
        )

        // Add storage permissions based on Android version
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.S_V2) {
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        } else {
            permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
            // Add notification permission for Android 13+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                permissions.add(Manifest.permission.POST_NOTIFICATIONS)
            }
        }

        return permissions
    }

    /**
     * Check if audio recording permission is granted
     */
    fun hasAudioPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * Composable to handle camera permission request
     */
    @OptIn(ExperimentalPermissionsApi::class)
    @Composable
    fun CameraPermissionHandler(
        onPermissionGranted: @Composable () -> Unit
    ) {
        val cameraPermissionState = rememberPermissionState(Manifest.permission.CAMERA)

        when {
            cameraPermissionState.status.isGranted -> {
                onPermissionGranted()
            }
            else -> {
                CameraPermissionRequest(cameraPermissionState)
            }
        }
    }

    /**
     * Composable to handle audio permission request
     */
    @OptIn(ExperimentalPermissionsApi::class)
    @Composable
    fun AudioPermissionHandler(
        onPermissionGranted: @Composable () -> Unit
    ) {
        val audioPermissionState = rememberPermissionState(Manifest.permission.RECORD_AUDIO)

        when {
            audioPermissionState.status.isGranted -> {
                onPermissionGranted()
            }
            else -> {
                AudioPermissionRequest(audioPermissionState)
            }
        }
    }

    /**
     * Composable to request camera permission
     */
    @OptIn(ExperimentalPermissionsApi::class)
    @Composable
    private fun CameraPermissionRequest(
        cameraPermissionState: PermissionState
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val textToShow = if (cameraPermissionState.status.shouldShowRationale) {
                "Camera access is required to detect screen changes. Please grant the permission."
            } else {
                "Camera permission is required for this app to work. Please grant the permission."
            }

            Text(textToShow)
            Spacer(modifier = Modifier.height(8.dp))
            Button(onClick = { cameraPermissionState.launchPermissionRequest() }) {
                Text("Request Camera Permission")
            }
        }
    }

    /**
     * Composable to request audio permission
     */
    @OptIn(ExperimentalPermissionsApi::class)
    @Composable
    private fun AudioPermissionRequest(
        audioPermissionState: PermissionState
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val textToShow = if (audioPermissionState.status.shouldShowRationale) {
                "Microphone access is required to detect mouse clicks. Please grant the permission."
            } else {
                "Microphone permission is required for mouse click detection. Please grant the permission."
            }

            Text(textToShow)
            Spacer(modifier = Modifier.height(8.dp))
            Button(onClick = { audioPermissionState.launchPermissionRequest() }) {
                Text("Request Microphone Permission")
            }
        }
    }

    /**
     * Composable to handle both camera and audio permissions
     */
    @OptIn(ExperimentalPermissionsApi::class)
    @Composable
    fun CameraAndAudioPermissionHandler(
        onPermissionsGranted: @Composable () -> Unit
    ) {
        CameraPermissionHandler {
            AudioPermissionHandler {
                onPermissionsGranted()
            }
        }
    }
}
