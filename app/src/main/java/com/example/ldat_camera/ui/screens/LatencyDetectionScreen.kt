package com.example.ldat_camera.ui.screens

import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Slider
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.example.ldat_camera.detection.LatencyDetector
import com.example.ldat_camera.detection.RegionOfInterest
import com.example.ldat_camera.latency.LatencyManager
import com.example.ldat_camera.latency.LatencyMeasurement
import com.example.ldat_camera.permissions.PermissionsHandler
import com.example.ldat_camera.ui.components.CameraPreview
import com.example.ldat_camera.ui.components.RegionSelector

/**
 * Main screen for latency detection
 */
@Composable
fun LatencyDetectionScreen() {
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        PermissionsHandler.CameraPermissionHandler {
            LatencyDetectionContent()
        }
    }
}

/**
 * Main content for latency detection
 */
@Composable
fun LatencyDetectionContent() {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    var previewView by remember { mutableStateOf<PreviewView?>(null) }
    var isDetecting by remember { mutableStateOf(false) }
    var showRoiSelector by remember { mutableStateOf(false) }
    var luminanceThreshold by remember { mutableFloatStateOf(0.1f) }
    var colorThreshold by remember { mutableFloatStateOf(0.15f) }
    var skipFrames by remember { mutableIntStateOf(2) }
    var regionOfInterest by remember { mutableStateOf<RegionOfInterest?>(null) }

    // Store latency measurements
    val measurements = remember { mutableStateListOf<LatencyMeasurement>() }
    var lastMeasurement by remember { mutableStateOf<LatencyMeasurement?>(null) }
    var lastChangeTimestamp by remember { mutableLongStateOf(0L) }
    var changeCount by remember { mutableIntStateOf(0) }

    // Create latency manager
    var latencyManager by remember { mutableStateOf<LatencyManager?>(null) }

    // Latency detection listener
    val latencyListener = remember {
        object : LatencyDetector.LatencyDetectionListener {
            override fun onFrameProcessed(
                timestamp: Long,
                currentLuminance: Float,
                currentColor: Int,
                luminanceChange: Float,
                colorChange: Float,
                isLuminanceChange: Boolean,
                isColorChange: Boolean,
                frameRate: Float
            ) {
                val measurement = LatencyMeasurement(
                    timestamp,
                    luminanceChange,
                    colorChange,
                    isLuminanceChange,
                    isColorChange
                )

                if (isLuminanceChange || isColorChange) {
                    lastMeasurement = measurement
                    lastChangeTimestamp = timestamp
                    changeCount++

                    // Add to measurements list (limit to last 100)
                    measurements.add(measurement)
                    if (measurements.size > 100) {
                        measurements.removeAt(0)
                    }
                }
            }
        }
    }

    // Clean up when the composable is disposed
    DisposableEffect(lifecycleOwner) {
        onDispose {
            latencyManager?.stopCamera()
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Camera preview
        CameraPreview(
            modifier = Modifier.fillMaxSize(),
            onPreviewViewCreated = { view ->
                previewView = view

                // Initialize latency manager with the new preview view
                if (latencyManager == null) {
                    latencyManager = LatencyManager(
                        context = context,
                        lifecycleOwner = lifecycleOwner,
                        previewView = view,
                        latencyListener = latencyListener
                    )

                    // Start the camera
                    latencyManager?.startCamera()
                    isDetecting = true
                }
            }
        )

        // Region selector overlay (when active)
        if (showRoiSelector) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .zIndex(10f)
                    .background(Color.Black.copy(alpha = 0.5f))
            ) {
                RegionSelector(
                    initialRegion = regionOfInterest,
                    onRegionSelected = { roi ->
                        regionOfInterest = roi
                        // Region selection is no longer used
                    }
                )

                // Done button
                Button(
                    onClick = { showRoiSelector = false },
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 16.dp)
                ) {
                    Text("Done")
                }
            }
        }

        // Controls at the bottom
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .background(MaterialTheme.colorScheme.surface.copy(alpha = 0.8f))
                .padding(16.dp)
        ) {
            // Last measurement display
            lastMeasurement?.let { measurement ->
                Text(
                    text = "Change Detected: ${measurement.getChangeType()}",
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = "Luminance Change: ${String.format("%.2f", measurement.luminanceChange * 100)}%",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "Color Change: ${String.format("%.2f", measurement.colorChange * 100)}%",
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            Text(
                text = "Total Changes Detected: $changeCount",
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(vertical = 8.dp)
            )

            // ROI selector button
            Button(
                onClick = { showRoiSelector = !showRoiSelector },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(if (showRoiSelector) "Cancel Selection" else "Select Region of Interest")
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Threshold controls
            Text(
                text = "Luminance Threshold: ${String.format("%.1f", luminanceThreshold * 100)}%",
                style = MaterialTheme.typography.bodyMedium
            )
            Slider(
                value = luminanceThreshold,
                onValueChange = {
                    luminanceThreshold = it
                    // Parameter updates are no longer used
                },
                valueRange = 0.01f..0.3f
            )

            Text(
                text = "Color Threshold: ${String.format("%.1f", colorThreshold * 100)}%",
                style = MaterialTheme.typography.bodyMedium
            )
            Slider(
                value = colorThreshold,
                onValueChange = {
                    colorThreshold = it
                    // Parameter updates are no longer used
                },
                valueRange = 0.01f..0.3f
            )

            // Frame skip control
            Text(
                text = "Process every $skipFrames frames",
                style = MaterialTheme.typography.bodyMedium
            )
            Slider(
                value = skipFrames.toFloat(),
                onValueChange = {
                    skipFrames = it.toInt().coerceAtLeast(1)
                    // Parameter updates are no longer used
                },
                valueRange = 1f..10f,
                steps = 9
            )

            // Reset button
            Button(
                onClick = { changeCount = 0; measurements.clear() },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Reset Counter")
            }
        }

        // App title at the top
        Text(
            text = "Latency Detector",
            modifier = Modifier
                .align(Alignment.TopCenter)
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.primary)
                .padding(16.dp),
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onPrimary
        )
    }
}
