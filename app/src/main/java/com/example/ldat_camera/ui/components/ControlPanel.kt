package com.example.ldat_camera.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.ui.Alignment as ComposeAlignment
import androidx.compose.material3.Button
import androidx.compose.material3.Slider
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * Control panel for adjusting detection settings and displaying status
 */
@Composable
fun ControlPanel(
    isDetecting: Boolean,
    lastChangePercentage: Float,
    onStartDetection: () -> Unit,
    onStopDetection: () -> Unit,
    onParametersChanged: (Float, Int, Float) -> Unit,
    enableNotifications: Boolean = true,
    onNotificationsToggled: ((Boolean) -> Unit)? = null,
    lastChangeType: String = "None",
    changeCounter: Int = 0,
    onResetCounter: () -> Unit = {}
) {
    var sensitivity by remember { mutableFloatStateOf(0.05f) }
    var frameSkip by remember { mutableIntStateOf(10) }
    var brightnessThreshold by remember { mutableFloatStateOf(0.15f) }
    var notificationsEnabled by remember { mutableStateOf(enableNotifications) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = ComposeAlignment.CenterHorizontally
    ) {
        // Status display
        Text(
            text = if (isDetecting) "Status: Detecting Changes" else "Status: Camera Ready (Not Detecting Changes)",
            modifier = Modifier.padding(bottom = 8.dp)
        )

        // Change counter display
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 4.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = ComposeAlignment.CenterVertically
        ) {
            Text(
                text = "Changes Detected: $changeCounter",
                modifier = Modifier.padding(end = 8.dp)
            )

            Button(
                onClick = onResetCounter,
                modifier = Modifier.padding(start = 8.dp)
            ) {
                Text("Reset Counter")
            }
        }

        if (lastChangePercentage > 0) {
            Text(
                text = "Last Change: ${String.format("%.2f", lastChangePercentage * 100)}% ($lastChangeType)",
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }

        // Control buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Button(
                onClick = onStartDetection,
                enabled = !isDetecting
            ) {
                Text("Start Change Detection")
            }

            Button(
                onClick = onStopDetection,
                enabled = isDetecting
            ) {
                Text("Stop Change Detection")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Sensitivity slider (for content changes)
        Text(
            text = "Content Sensitivity: ${String.format("%.1f", sensitivity * 100)}%",
            modifier = Modifier.padding(top = 8.dp)
        )
        Slider(
            value = sensitivity,
            onValueChange = {
                sensitivity = it
                onParametersChanged(sensitivity, frameSkip, brightnessThreshold)
            },
            valueRange = 0.01f..0.2f
        )

        // Brightness threshold slider
        Text(
            text = "Brightness Sensitivity: ${String.format("%.1f", brightnessThreshold * 100)}%",
            modifier = Modifier.padding(top = 8.dp)
        )
        Slider(
            value = brightnessThreshold,
            onValueChange = {
                brightnessThreshold = it
                onParametersChanged(sensitivity, frameSkip, brightnessThreshold)
            },
            valueRange = 0.05f..0.3f
        )

        // Frame skip slider
        Text(
            text = "Process every $frameSkip frames",
            modifier = Modifier.padding(top = 8.dp)
        )
        Slider(
            value = frameSkip.toFloat(),
            onValueChange = {
                frameSkip = it.toInt()
                onParametersChanged(sensitivity, frameSkip, brightnessThreshold)
            },
            valueRange = 1f..30f,
            steps = 29
        )

        // Notifications toggle
        Spacer(modifier = Modifier.height(16.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = ComposeAlignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text("Enable Notifications")
            Switch(
                checked = notificationsEnabled,
                onCheckedChange = { isEnabled ->
                    notificationsEnabled = isEnabled
                    onNotificationsToggled?.invoke(isEnabled)
                }
            )
        }
    }
}
