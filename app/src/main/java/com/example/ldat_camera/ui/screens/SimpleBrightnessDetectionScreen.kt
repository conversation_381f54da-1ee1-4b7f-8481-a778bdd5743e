package com.example.ldat_camera.ui.screens

import android.graphics.Color
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Slider
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.ldat_camera.detection.LatencyDetector
import com.example.ldat_camera.latency.LatencyManager
import com.example.ldat_camera.permissions.PermissionsHandler
import com.example.ldat_camera.ui.components.CameraPreview
import com.example.ldat_camera.utils.AudioClickDetector
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import kotlin.math.roundToInt

/**
 * A simplified screen that just shows brightness and color values
 */
@Composable
fun SimpleBrightnessDetectionScreen() {
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        PermissionsHandler.CameraAndAudioPermissionHandler {
            SimpleBrightnessContent()
        }
    }
}

@Composable
fun SimpleBrightnessContent() {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val coroutineScope = rememberCoroutineScope()

    var previewView by remember { mutableStateOf<PreviewView?>(null) }

    // Values to display
    var currentLuminance by remember { mutableFloatStateOf(0f) }
    var currentColor by remember { mutableIntStateOf(Color.BLACK) }
    var luminanceChange by remember { mutableFloatStateOf(0f) }
    var colorChange by remember { mutableFloatStateOf(0f) }
    var frameCount by remember { mutableIntStateOf(0) }
    var frameRate by remember { mutableFloatStateOf(0f) }
    var lastTimestamp by remember { mutableLongStateOf(0L) }

    // Counters for changes
    var brightnessChangeCount by remember { mutableIntStateOf(0) }
    var colorChangeCount by remember { mutableIntStateOf(0) }

    // Mouse click detection
    var clickCount by remember { mutableIntStateOf(0) }
    var lastClickTime by remember { mutableLongStateOf(0L) }
    var isClickDetected by remember { mutableStateOf(false) }

    // Latency frame tracking
    var latencyFrameStart by remember { mutableIntStateOf(0) }
    var latencyFrameEnd by remember { mutableIntStateOf(0) }
    var latencyFrameTotal by remember { mutableIntStateOf(0) }
    var isTrackingLatency by remember { mutableStateOf(false) }

    // Thresholds for counting changes
    val brightnessThreshold = 0.015f // 1.5% change
    val colorThreshold = 0.02f // 2% change

    // Exposure control
    var exposureLevel by remember { mutableIntStateOf(0) } // Default to middle exposure
    var exposureRange by remember { mutableStateOf<Pair<Int, Int>?>(Pair(-10, 10)) } // Default range

    // Create latency manager
    var latencyManager by remember { mutableStateOf<LatencyManager?>(null) }

    // Create audio click detector
    val audioClickDetector = remember {
        AudioClickDetector(coroutineScope, context).apply {
            onClickDetected = { timestamp ->
                clickCount = getClickCount()
                lastClickTime = timestamp

                // Start latency frame tracking
                latencyFrameStart = frameCount
                isTrackingLatency = true

                // Visual feedback - flash the click detection indicator
                isClickDetected = true
                coroutineScope.launch {
                    delay(500) // Show indicator for 500ms
                    isClickDetected = false
                }
            }
        }
    }

    // Create a class to handle the listener
    class FrameListener(
        private val onFrame: (
            luminance: Float,
            color: Int,
            luminanceDiff: Float,
            colorDiff: Float,
            timestamp: Long,
            fps: Float
        ) -> Unit
    ) : LatencyDetector.LatencyDetectionListener {
        override fun onFrameProcessed(
            timestamp: Long,
            currentLuminance: Float,
            currentColor: Int,
            luminanceChange: Float,
            colorChange: Float,
            isLuminanceChange: Boolean,
            isColorChange: Boolean,
            frameRate: Float
        ) {
            onFrame(currentLuminance, currentColor, luminanceChange, colorChange, timestamp, frameRate)
        }
    }

    // Latency detection listener
    val latencyListener = remember {
        FrameListener { lum, col, lumDiff, colDiff, time, fps ->
            currentLuminance = lum
            currentColor = col
            luminanceChange = lumDiff
            colorChange = colDiff
            frameCount++
            lastTimestamp = time
            frameRate = fps

            // Check if brightness change exceeds threshold
            if (lumDiff > brightnessThreshold) {
                brightnessChangeCount++

                // If we're tracking latency and detect a brightness change, end tracking
                if (isTrackingLatency) {
                    latencyFrameEnd = frameCount
                    latencyFrameTotal = latencyFrameEnd - latencyFrameStart
                    isTrackingLatency = false
                }
            }

            // Check if color change exceeds threshold
            if (colDiff > colorThreshold) {
                colorChangeCount++

                // If we're tracking latency and detect a color change, end tracking
                if (isTrackingLatency) {
                    latencyFrameEnd = frameCount
                    latencyFrameTotal = latencyFrameEnd - latencyFrameStart
                    isTrackingLatency = false
                }
            }
        }
    }

    // Clean up when the composable is disposed
    DisposableEffect(lifecycleOwner) {
        // Start the audio click detector
        audioClickDetector.startListening()

        onDispose {
            latencyManager?.stopCamera()
            audioClickDetector.stopListening()
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Camera preview
        CameraPreview(
            modifier = Modifier.fillMaxSize(),
            onPreviewViewCreated = { view ->
                previewView = view

                // Initialize latency manager with the new preview view
                if (latencyManager == null) {
                    latencyManager = LatencyManager(
                        context = context,
                        lifecycleOwner = lifecycleOwner,
                        previewView = view,
                        latencyListener = latencyListener
                    )

                    // Start the camera
                    latencyManager?.startCamera()

                    // Get the actual exposure range from the device
                    latencyManager?.getExposureRange()?.let { range ->
                        exposureRange = range
                    }
                }
            }
        )

        // Information overlay
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.TopCenter)
                .background(androidx.compose.ui.graphics.Color.Black.copy(alpha = 0.7f))
                .padding(16.dp)
        ) {
            Text(
                text = "Camera Values",
                style = MaterialTheme.typography.headlineSmall,
                color = androidx.compose.ui.graphics.Color.White,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Brightness value
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 4.dp)
            ) {
                Text(
                    text = "Brightness:",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = String.format("%.2f", currentLuminance),
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 18.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "(${(currentLuminance * 100).roundToInt()}%)",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 16.sp
                )
            }

            // Brightness change
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 4.dp)
            ) {
                Text(
                    text = "Brightness Change:",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = String.format("%.4f", luminanceChange),
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 18.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "(${(luminanceChange * 100).roundToInt()}%)",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 16.sp
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Color value
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 4.dp)
            ) {
                Text(
                    text = "Color (RGB):",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                )
                Spacer(modifier = Modifier.width(8.dp))

                // Color square
                Canvas(
                    modifier = Modifier
                        .size(24.dp)
                        .background(androidx.compose.ui.graphics.Color(currentColor))
                ) {}

                Spacer(modifier = Modifier.width(8.dp))

                // RGB values
                Text(
                    text = "R: ${Color.red(currentColor)}, G: ${Color.green(currentColor)}, B: ${Color.blue(currentColor)}",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 16.sp
                )
            }

            // Color change
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 4.dp)
            ) {
                Text(
                    text = "Color Change:",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = String.format("%.4f", colorChange),
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 18.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "(${(colorChange * 100).roundToInt()}%)",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 16.sp
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Counters section
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 4.dp)
            ) {
                Text(
                    text = "Frames Processed: $frameCount",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 16.sp
                )
                Spacer(modifier = Modifier.width(16.dp))
                Text(
                    text = "Frame Rate: ${String.format("%.1f", frameRate)} fps",
                    color = androidx.compose.ui.graphics.Color.Yellow,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
            }

            // Latency frame tracking
            if (isTrackingLatency) {
                Text(
                    text = "Latency Frame Start: $latencyFrameStart (tracking...)",
                    color = androidx.compose.ui.graphics.Color.Yellow,
                    fontSize = 16.sp
                )
            } else if (latencyFrameTotal > 0) {
                Text(
                    text = "Latency Frame Start: $latencyFrameStart",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 16.sp
                )
                Text(
                    text = "Latency Frame End: $latencyFrameEnd",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 16.sp
                )
                Text(
                    text = "Latency Frame Total: $latencyFrameTotal",
                    color = androidx.compose.ui.graphics.Color.Green,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Change counters with thresholds
            Text(
                text = "Change Counters",
                color = androidx.compose.ui.graphics.Color.White,
                fontWeight = FontWeight.Bold,
                fontSize = 18.sp
            )

            // Mouse click detection
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 4.dp)
            ) {
                Text(
                    text = "Mouse Clicks:",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "$clickCount",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 16.sp
                )

                if (lastClickTime > 0) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "(Last: ${lastClickTime % 100000}ms)",
                        color = androidx.compose.ui.graphics.Color.White,
                        fontSize = 14.sp
                    )
                }

                // Visual indicator for click detection
                Spacer(modifier = Modifier.width(8.dp))
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .background(
                            color = if (isClickDetected)
                                androidx.compose.ui.graphics.Color.Green
                            else
                                androidx.compose.ui.graphics.Color.DarkGray,
                            shape = androidx.compose.foundation.shape.CircleShape
                        )
                )
            }

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 4.dp)
            ) {
                Text(
                    text = "Brightness Changes (>${(brightnessThreshold * 100).toInt()}%):",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "$brightnessChangeCount",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 16.sp
                )
            }

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 4.dp)
            ) {
                Text(
                    text = "Color Changes (>${(colorThreshold * 100).toInt()}%):",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "$colorChangeCount",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 16.sp
                )
            }

            // Reset button
            Spacer(modifier = Modifier.height(8.dp))
            Button(
                onClick = {
                    brightnessChangeCount = 0
                    colorChangeCount = 0
                    audioClickDetector.resetClickCount()
                    clickCount = 0
                    lastClickTime = 0L
                    isClickDetected = false

                    // Reset latency tracking
                    latencyFrameStart = 0
                    latencyFrameEnd = 0
                    latencyFrameTotal = 0
                    isTrackingLatency = false
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor = androidx.compose.ui.graphics.Color.Red
                )
            ) {
                Text(
                    text = "Reset Counters",
                    color = androidx.compose.ui.graphics.Color.White
                )
            }

            // Exposure control slider
            exposureRange?.let { range ->
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Camera Exposure: $exposureLevel (Range: ${range.first} to ${range.second})",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )

                Slider(
                    value = exposureLevel.toFloat(),
                    onValueChange = { newValue ->
                        val newExposure = newValue.toInt()
                        // Update the actual exposure level based on what the camera accepted
                        latencyManager?.setExposureLevel(newExposure)?.let { actualLevel ->
                            exposureLevel = actualLevel
                        } ?: run {
                            exposureLevel = newExposure
                        }
                    },
                    valueRange = range.first.toFloat()..range.second.toFloat(),
                    steps = (range.second - range.first),
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            } ?: run {
                // Fallback if exposure control is not supported
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Exposure control not supported on this device",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
            }

            // Add min/max labels
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                horizontalArrangement = androidx.compose.foundation.layout.Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Darker",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 12.sp
                )
                Text(
                    text = "Brighter",
                    color = androidx.compose.ui.graphics.Color.White,
                    fontSize = 12.sp
                )
            }
        }
    }
}
