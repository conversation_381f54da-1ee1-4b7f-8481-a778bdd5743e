package com.example.ldat_camera.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntSize
import com.example.ldat_camera.detection.RegionOfInterest
import kotlin.math.abs

/**
 * A composable that allows the user to select a region of interest on the screen
 */
@Composable
fun RegionSelector(
    initialRegion: RegionOfInterest? = null,
    onRegionSelected: (RegionOfInterest) -> Unit
) {
    var size by remember { mutableStateOf(IntSize.Zero) }
    var startPoint by remember { mutableStateOf(Offset.Zero) }
    var endPoint by remember { mutableStateOf(Offset.Zero) }
    var isDragging by remember { mutableStateOf(false) }
    
    // Initialize with the provided region if available
    val density = LocalDensity.current
    
    // Initialize points if we have an initial region
    if (initialRegion != null && size.width > 0 && startPoint == Offset.Zero && endPoint == Offset.Zero) {
        startPoint = Offset(initialRegion.left, initialRegion.top)
        endPoint = Offset(initialRegion.right, initialRegion.bottom)
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .onSizeChanged { newSize ->
                size = newSize
                
                // If we don't have an initial region, create a default one
                if (initialRegion == null && startPoint == Offset.Zero && endPoint == Offset.Zero) {
                    val defaultRegion = RegionOfInterest.createDefault(newSize.width, newSize.height)
                    startPoint = Offset(defaultRegion.left, defaultRegion.top)
                    endPoint = Offset(defaultRegion.right, defaultRegion.bottom)
                    onRegionSelected(defaultRegion)
                }
            }
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = { offset ->
                        // Determine if we're starting a new selection or adjusting the existing one
                        val existingRegion = RegionOfInterest(
                            startPoint.x,
                            startPoint.y,
                            endPoint.x,
                            endPoint.y
                        )
                        
                        // If the tap is inside the existing region, move the entire region
                        if (offset.x in existingRegion.left..existingRegion.right &&
                            offset.y in existingRegion.top..existingRegion.bottom) {
                            isDragging = true
                        } else {
                            // Start a new selection
                            startPoint = offset
                            endPoint = offset
                            isDragging = false
                        }
                    },
                    onDrag = { change, dragAmount ->
                        change.consume()
                        
                        if (isDragging) {
                            // Move the entire region
                            startPoint = Offset(
                                (startPoint.x + dragAmount.x).coerceIn(0f, size.width.toFloat()),
                                (startPoint.y + dragAmount.y).coerceIn(0f, size.height.toFloat())
                            )
                            endPoint = Offset(
                                (endPoint.x + dragAmount.x).coerceIn(0f, size.width.toFloat()),
                                (endPoint.y + dragAmount.y).coerceIn(0f, size.height.toFloat())
                            )
                        } else {
                            // Adjust the end point of the selection
                            endPoint = Offset(
                                (endPoint.x + dragAmount.x).coerceIn(0f, size.width.toFloat()),
                                (endPoint.y + dragAmount.y).coerceIn(0f, size.height.toFloat())
                            )
                        }
                        
                        // Notify about the new region
                        val newRegion = RegionOfInterest(
                            minOf(startPoint.x, endPoint.x),
                            minOf(startPoint.y, endPoint.y),
                            maxOf(startPoint.x, endPoint.x),
                            maxOf(startPoint.y, endPoint.y)
                        )
                        
                        if (newRegion.isValid() && 
                            abs(newRegion.width()) > 20 && 
                            abs(newRegion.height()) > 20) {
                            onRegionSelected(newRegion)
                        }
                    },
                    onDragEnd = {
                        isDragging = false
                    }
                )
            }
    ) {
        // Draw the selection rectangle
        Canvas(modifier = Modifier.fillMaxSize()) {
            if (startPoint != Offset.Zero && endPoint != Offset.Zero) {
                // Draw the selection rectangle
                drawRect(
                    color = Color.Red,
                    topLeft = Offset(
                        minOf(startPoint.x, endPoint.x),
                        minOf(startPoint.y, endPoint.y)
                    ),
                    size = androidx.compose.ui.geometry.Size(
                        abs(endPoint.x - startPoint.x),
                        abs(endPoint.y - startPoint.y)
                    ),
                    style = Stroke(width = 2f)
                )
                
                // Draw semi-transparent fill
                drawRect(
                    color = Color.Red.copy(alpha = 0.2f),
                    topLeft = Offset(
                        minOf(startPoint.x, endPoint.x),
                        minOf(startPoint.y, endPoint.y)
                    ),
                    size = androidx.compose.ui.geometry.Size(
                        abs(endPoint.x - startPoint.x),
                        abs(endPoint.y - startPoint.y)
                    )
                )
            }
        }
    }
}
