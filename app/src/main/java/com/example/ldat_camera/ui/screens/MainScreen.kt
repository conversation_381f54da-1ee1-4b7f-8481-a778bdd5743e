package com.example.ldat_camera.ui.screens

import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import com.example.ldat_camera.camera.CameraManager
import com.example.ldat_camera.detection.ScreenChangeDetector
import com.example.ldat_camera.notifications.NotificationHelper
import com.example.ldat_camera.permissions.PermissionsHandler
import com.example.ldat_camera.ui.components.CameraPreview
import com.example.ldat_camera.ui.components.ControlPanel

/**
 * Main screen of the app
 */
@Composable
fun MainScreen() {
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        PermissionsHandler.CameraPermissionHandler {
            ScreenChangeDetectionContent()
        }
    }
}

/**
 * Main content of the screen change detection app
 */
@Composable
fun ScreenChangeDetectionContent() {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    var previewView by remember { mutableStateOf<PreviewView?>(null) }
    var isDetecting by remember { mutableStateOf(false) }
    var lastChangePercentage by remember { mutableFloatStateOf(0f) }
    var sensitivity by remember { mutableFloatStateOf(0.05f) }
    var frameSkip by remember { mutableStateOf(10) }
    var brightnessThreshold by remember { mutableFloatStateOf(0.15f) }
    var enableNotifications by remember { mutableStateOf(true) }
    var lastChangeType by remember { mutableStateOf("None") }
    var changeCounter by remember { mutableStateOf(0) }

    // Create notification helper
    val notificationHelper = remember { NotificationHelper(context) }

    // Create camera manager reference
    var cameraManager by remember { mutableStateOf<CameraManager?>(null) }

    // Clean up when the composable is disposed
    DisposableEffect(lifecycleOwner) {
        onDispose {
            cameraManager?.stopCamera()
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Camera preview
        CameraPreview(
            modifier = Modifier.fillMaxSize(),
            onPreviewViewCreated = { view ->
                previewView = view
                // Initialize camera manager with the new preview view
                cameraManager = CameraManager(
                    context = context,
                    lifecycleOwner = lifecycleOwner,
                    previewView = view,
                    screenChangeListener = object : ScreenChangeDetector.ScreenChangeListener {
                        override fun onScreenChangeDetected(
                            changePercentage: Float,
                            brightnessChange: Float,
                            isBrightnessChange: Boolean
                        ) {
                            lastChangePercentage = if (isBrightnessChange) brightnessChange else changePercentage
                            lastChangeType = if (isBrightnessChange) "Brightness" else "Content"

                            // Increment the change counter
                            changeCounter++

                            // Show notification if enabled
                            if (enableNotifications) {
                                notificationHelper.showScreenChangeNotification(
                                    changePercentage = lastChangePercentage,
                                    changeType = lastChangeType,
                                    changeCount = changeCounter
                                )
                            }
                        }
                    }
                )

                // Always start the camera preview, but only start detection if it was already on
                cameraManager?.startCamera(sensitivity, frameSkip, brightnessThreshold)
                isDetecting = true
            }
        )

        // Control panel at the bottom
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
        ) {
            ControlPanel(
                isDetecting = isDetecting,
                lastChangePercentage = lastChangePercentage,
                onStartDetection = {
                    cameraManager?.startCamera(sensitivity, frameSkip, brightnessThreshold)
                    isDetecting = true
                },
                onStopDetection = {
                    cameraManager?.stopCamera()
                    isDetecting = false
                },
                onParametersChanged = { newSensitivity, newFrameSkip, newBrightnessThreshold ->
                    sensitivity = newSensitivity
                    frameSkip = newFrameSkip
                    brightnessThreshold = newBrightnessThreshold
                    if (isDetecting) {
                        cameraManager?.updateParameters(sensitivity, frameSkip, brightnessThreshold)
                    }
                },
                enableNotifications = enableNotifications,
                onNotificationsToggled = { isEnabled ->
                    enableNotifications = isEnabled
                },
                lastChangeType = lastChangeType,
                changeCounter = changeCounter,
                onResetCounter = {
                    changeCounter = 0
                }
            )
        }

        // App title at the top
        Text(
            text = "PC Screen Change Detector",
            modifier = Modifier
                .align(Alignment.TopCenter)
                .fillMaxWidth(),
            style = MaterialTheme.typography.headlineSmall
        )
    }
}
